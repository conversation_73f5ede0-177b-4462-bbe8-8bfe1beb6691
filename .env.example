# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/kairos_graphrag

# OpenAI Configuration (for embeddings and LLM)
OPENAI_API_KEY=your_openai_api_key_here

# Embedding Configuration
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_DIMENSION=384

# GraphRAG Configuration
MAX_CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_ENTITIES_PER_CHUNK=50
SIMILARITY_THRESHOLD=0.7

# NLP Configuration
SPACY_MODEL=en_core_web_sm

# Logging
LOG_LEVEL=INFO
