import socketio
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.routers.user_router import router as user_router
from app.routers.project_router import router as project_router
from app.managers.ProjectManager import ProjectManager
import uvicorn

# Create FastAPI app
app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Include routers
app.include_router(user_router, prefix="/api")
app.include_router(project_router, prefix="/api")

@app.get("/")
async def root():
    return {"message": "Kairos API is running with Socket.IO!"}

# --- Socket.IO Setup ---
sio = socketio.AsyncServer(async_mode='asgi', cors_allowed_origins='*')
sio_app = socketio.ASGIApp(sio, other_asgi_app=app)

@sio.event
async def connect(sid, environ, auth):
    print('Client connected:', sid)
    # Do not return False or raise

@sio.event
async def disconnect(sid):
    print('Client disconnected:', sid)

@sio.event
async def join_project(sid, data):
    project_id = data.get("project_id")
    project = ProjectManager.from_project_id(project_id)
    if not project:
        await sio.emit("error", {"message": "Project not found."}, to=sid)
        return
    await sio.save_session(sid, {"project_id": project_id})
    await sio.emit("joined_project", {"project_id": project_id}, to=sid)

@sio.event
async def project_message(sid, data):
    session = await sio.get_session(sid)
    project_id = session.get("project_id")
    message = data.get("message")
    project = ProjectManager.from_project_id(project_id)
    if not project:
        await sio.emit("error", {"message": "Project not found."}, to=sid)
        return
    reply = project.talk(message, role="user")
    await sio.emit("project_message", {"message": reply}, to=sid)

if __name__ == "__main__":
    uvicorn.run(sio_app, host="0.0.0.0", port=8000, reload=True)