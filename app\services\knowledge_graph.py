"""
Knowledge Graph construction and management for GraphRAG
Uses NetworkX for graph operations and community detection
"""
import logging
from typing import List, Dict, Any, Set, Tuple, Optional
import json
from collections import defaultdict

# Graph processing
import networkx as nx
from networkx.algorithms import community

# Database
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from app.config.database import get_db
from app.models.graphrag_models import Entity, Relationship, GraphSummary

logger = logging.getLogger(__name__)

class KnowledgeGraphBuilder:
    """
    Builds and manages knowledge graphs from extracted entities and relationships
    """
    
    def __init__(self):
        self.graph = nx.Graph()
        self.directed_graph = nx.DiGraph()
    
    def build_graph_from_database(self, db: Session = None) -> Dict[str, Any]:
        """
        Build NetworkX graph from database entities and relationships
        """
        if db is None:
            db = next(get_db())
        
        try:
            # Clear existing graphs
            self.graph.clear()
            self.directed_graph.clear()
            
            # Load entities
            entities = db.query(Entity).all()
            entity_map = {}
            
            for entity in entities:
                node_id = str(entity.id)
                entity_map[node_id] = entity
                
                # Add node to both graphs
                node_attrs = {
                    'name': entity.name,
                    'type': entity.entity_type,
                    'frequency': entity.frequency,
                    'description': entity.description
                }
                
                self.graph.add_node(node_id, **node_attrs)
                self.directed_graph.add_node(node_id, **node_attrs)
            
            # Load relationships
            relationships = db.query(Relationship).all()
            
            for rel in relationships:
                source_id = str(rel.source_entity_id)
                target_id = str(rel.target_entity_id)
                
                edge_attrs = {
                    'relationship_type': rel.relationship_type,
                    'confidence': rel.confidence,
                    'frequency': rel.frequency,
                    'description': rel.description
                }
                
                # Add edge to both graphs
                self.graph.add_edge(source_id, target_id, **edge_attrs)
                self.directed_graph.add_edge(source_id, target_id, **edge_attrs)
            
            stats = {
                'nodes': self.graph.number_of_nodes(),
                'edges': self.graph.number_of_edges(),
                'connected_components': nx.number_connected_components(self.graph)
            }
            
            logger.info(f"Built knowledge graph: {stats}")
            return {'success': True, 'stats': stats}
            
        except Exception as e:
            logger.error(f"Error building graph from database: {e}")
            return {'success': False, 'error': str(e)}
    
    def detect_communities(self, resolution: float = 1.0) -> Dict[str, List[str]]:
        """
        Detect communities in the graph using Louvain algorithm
        """
        try:
            # Use Louvain community detection
            communities = community.louvain_communities(self.graph, resolution=resolution)
            
            community_map = {}
            for i, comm in enumerate(communities):
                community_id = f"community_{i}"
                community_map[community_id] = list(comm)
            
            logger.info(f"Detected {len(community_map)} communities")
            return community_map
            
        except Exception as e:
            logger.error(f"Error detecting communities: {e}")
            return {}
    
    def generate_community_summary(self, community_nodes: List[str], 
                                 level: int = 0, db: Session = None) -> Optional[str]:
        """
        Generate a summary for a community of nodes
        """
        if db is None:
            db = next(get_db())
        
        try:
            # Get entity information for nodes in community
            entities = db.query(Entity).filter(Entity.id.in_(community_nodes)).all()
            
            if not entities:
                return None
            
            # Get relationships within the community
            relationships = db.query(Relationship).filter(
                and_(
                    Relationship.source_entity_id.in_(community_nodes),
                    Relationship.target_entity_id.in_(community_nodes)
                )
            ).all()
            
            # Generate summary text
            entity_names = [e.name for e in entities]
            entity_types = defaultdict(list)
            for e in entities:
                entity_types[e.entity_type].append(e.name)
            
            summary_parts = []
            
            # Describe entities by type
            for ent_type, names in entity_types.items():
                if len(names) == 1:
                    summary_parts.append(f"This community contains the {ent_type.lower()} {names[0]}")
                else:
                    summary_parts.append(f"This community contains {len(names)} {ent_type.lower()}s: {', '.join(names[:5])}")
                    if len(names) > 5:
                        summary_parts[-1] += f" and {len(names) - 5} others"
            
            # Describe key relationships
            rel_types = defaultdict(int)
            for rel in relationships:
                rel_types[rel.relationship_type] += 1
            
            if rel_types:
                rel_desc = []
                for rel_type, count in sorted(rel_types.items(), key=lambda x: x[1], reverse=True)[:3]:
                    rel_desc.append(f"{count} {rel_type} relationships")
                summary_parts.append(f"Key relationships include: {', '.join(rel_desc)}")
            
            summary = ". ".join(summary_parts) + "."
            return summary
            
        except Exception as e:
            logger.error(f"Error generating community summary: {e}")
            return None
    
    def create_hierarchical_summaries(self, max_levels: int = 3, db: Session = None) -> Dict[str, Any]:
        """
        Create hierarchical summaries of the knowledge graph
        """
        if db is None:
            db = next(get_db())
        
        try:
            # Clear existing summaries
            db.query(GraphSummary).delete()
            
            summaries_created = 0
            
            for level in range(max_levels):
                # Adjust resolution for different levels (higher = more communities)
                resolution = 1.0 + (level * 0.5)
                communities = self.detect_communities(resolution=resolution)
                
                if not communities:
                    break
                
                for community_id, node_ids in communities.items():
                    if len(node_ids) < 2:  # Skip single-node communities
                        continue
                    
                    summary_text = self.generate_community_summary(node_ids, level, db)
                    
                    if summary_text:
                        # Create title from most frequent entities
                        entities = db.query(Entity).filter(Entity.id.in_(node_ids)).order_by(Entity.frequency.desc()).limit(3).all()
                        title = f"Community: {', '.join([e.name for e in entities])}"
                        
                        # Get relationship IDs for this community
                        relationships = db.query(Relationship).filter(
                            and_(
                                Relationship.source_entity_id.in_(node_ids),
                                Relationship.target_entity_id.in_(node_ids)
                            )
                        ).all()
                        
                        graph_summary = GraphSummary(
                            level=level,
                            community_id=f"{level}_{community_id}",
                            title=title,
                            summary=summary_text,
                            entities=node_ids,
                            relationships=[str(r.id) for r in relationships]
                        )
                        
                        db.add(graph_summary)
                        summaries_created += 1
            
            db.commit()
            
            result = {
                'success': True,
                'summaries_created': summaries_created,
                'levels': max_levels
            }
            
            logger.info(f"Created hierarchical summaries: {result}")
            return result
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating hierarchical summaries: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_entity_neighbors(self, entity_id: str, max_hops: int = 2) -> List[Dict[str, Any]]:
        """
        Get neighboring entities within specified number of hops
        """
        try:
            if entity_id not in self.graph:
                return []
            
            neighbors = []
            visited = set()
            
            # BFS to find neighbors within max_hops
            queue = [(entity_id, 0)]
            visited.add(entity_id)
            
            while queue:
                current_id, hops = queue.pop(0)
                
                if hops < max_hops:
                    for neighbor_id in self.graph.neighbors(current_id):
                        if neighbor_id not in visited:
                            visited.add(neighbor_id)
                            queue.append((neighbor_id, hops + 1))
                            
                            # Get edge data
                            edge_data = self.graph.get_edge_data(current_id, neighbor_id)
                            node_data = self.graph.nodes[neighbor_id]
                            
                            neighbors.append({
                                'entity_id': neighbor_id,
                                'name': node_data.get('name'),
                                'type': node_data.get('type'),
                                'hops': hops + 1,
                                'relationship': edge_data.get('relationship_type'),
                                'confidence': edge_data.get('confidence')
                            })
            
            return neighbors
            
        except Exception as e:
            logger.error(f"Error getting entity neighbors: {e}")
            return []
