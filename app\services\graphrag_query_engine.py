"""
GraphRAG Query Engine
Combines vector similarity search with graph traversal for enhanced retrieval
"""
import logging
from typing import List, Dict, Any, Optional, Set, Tuple
from collections import defaultdict
import json
import networkx as nx

# Database
from sqlalchemy.orm import Session
from sqlalchemy import text, and_, or_
from app.config.database import get_db
from app.models.graphrag_models import DocumentChunk, Entity, Relationship, GraphSummary

# Services
from app.services.embedding_service import EmbeddingService
from app.services.knowledge_graph import KnowledgeGraphBuilder

logger = logging.getLogger(__name__)

class GraphRAGQueryEngine:
    """
    Query engine that combines vector search with graph traversal
    """
    
    def __init__(self, embedding_service: EmbeddingService = None):
        self.embedding_service = embedding_service or EmbeddingService()
        self.knowledge_graph = KnowledgeGraphBuilder()
    
    def query(self, query_text: str, max_chunks: int = 10, 
              max_entities: int = 20, similarity_threshold: float = 0.7,
              include_graph_context: bool = True, db: Session = None) -> Dict[str, Any]:
        """
        Main query method that combines vector search with graph traversal
        """
        if db is None:
            db = next(get_db())
        
        try:
            # Generate query embedding
            query_embedding = self.embedding_service.generate_embedding(query_text)
            if not query_embedding:
                return {'success': False, 'error': 'Failed to generate query embedding'}
            
            # Step 1: Vector similarity search for relevant chunks
            similar_chunks = self.embedding_service.find_similar_chunks(
                query_embedding, 
                limit=max_chunks,
                threshold=similarity_threshold,
                db=db
            )
            
            # Step 2: Find relevant entities from query
            relevant_entities = self._find_relevant_entities(
                query_text, query_embedding, max_entities, db
            )
            
            # Step 3: Graph traversal for additional context
            graph_context = {}
            if include_graph_context and relevant_entities:
                graph_context = self._get_graph_context(relevant_entities, db)
            
            # Step 4: Get hierarchical summaries
            relevant_summaries = self._find_relevant_summaries(
                query_embedding, limit=5, db=db
            )
            
            # Step 5: Combine and rank results
            combined_context = self._combine_context(
                similar_chunks, relevant_entities, graph_context, relevant_summaries
            )
            
            result = {
                'success': True,
                'query': query_text,
                'chunks': similar_chunks,
                'entities': relevant_entities,
                'graph_context': graph_context,
                'summaries': relevant_summaries,
                'combined_context': combined_context
            }
            
            logger.info(f"GraphRAG query completed: {len(similar_chunks)} chunks, {len(relevant_entities)} entities")
            return result
            
        except Exception as e:
            logger.error(f"Error in GraphRAG query: {e}")
            return {'success': False, 'error': str(e)}
    
    def _find_relevant_entities(self, query_text: str, query_embedding: List[float],
                              max_entities: int, db: Session) -> List[Dict[str, Any]]:
        """
        Find entities relevant to the query using both text matching and embedding similarity
        """
        try:
            relevant_entities = []
            
            # Text-based entity search
            query_words = query_text.lower().split()
            text_entities = db.query(Entity).filter(
                or_(*[Entity.name.ilike(f'%{word}%') for word in query_words])
            ).limit(max_entities // 2).all()
            
            for entity in text_entities:
                relevant_entities.append({
                    'entity_id': str(entity.id),
                    'name': entity.name,
                    'type': entity.entity_type,
                    'frequency': entity.frequency,
                    'relevance_score': 0.8,  # High score for text matches
                    'match_type': 'text'
                })
            
            # Embedding-based entity search
            if query_embedding:
                embedding_query = text("""
                    SELECT 
                        id,
                        name,
                        entity_type,
                        frequency,
                        1 - (embedding <=> :query_embedding) as similarity
                    FROM entities 
                    WHERE embedding IS NOT NULL
                        AND 1 - (embedding <=> :query_embedding) > 0.6
                    ORDER BY embedding <=> :query_embedding
                    LIMIT :limit
                """)
                
                result = db.execute(embedding_query, {
                    'query_embedding': str(query_embedding),
                    'limit': max_entities // 2
                })
                
                existing_ids = {e['entity_id'] for e in relevant_entities}
                
                for row in result:
                    entity_id = str(row.id)
                    if entity_id not in existing_ids:
                        relevant_entities.append({
                            'entity_id': entity_id,
                            'name': row.name,
                            'type': row.entity_type,
                            'frequency': row.frequency,
                            'relevance_score': float(row.similarity),
                            'match_type': 'embedding'
                        })
            
            # Sort by relevance score
            relevant_entities.sort(key=lambda x: x['relevance_score'], reverse=True)
            return relevant_entities[:max_entities]
            
        except Exception as e:
            logger.error(f"Error finding relevant entities: {e}")
            return []
    
    def _get_graph_context(self, relevant_entities: List[Dict[str, Any]], 
                          db: Session) -> Dict[str, Any]:
        """
        Get graph context for relevant entities
        """
        try:
            # Build graph from database
            self.knowledge_graph.build_graph_from_database(db)
            
            graph_context = {
                'entity_relationships': {},
                'connected_entities': {},
                'relationship_paths': []
            }
            
            entity_ids = [e['entity_id'] for e in relevant_entities]
            
            # Get direct relationships for each entity
            for entity_data in relevant_entities:
                entity_id = entity_data['entity_id']
                
                # Get neighbors
                neighbors = self.knowledge_graph.get_entity_neighbors(entity_id, max_hops=2)
                graph_context['connected_entities'][entity_id] = neighbors
                
                # Get direct relationships
                relationships = db.query(Relationship).filter(
                    or_(
                        Relationship.source_entity_id == entity_id,
                        Relationship.target_entity_id == entity_id
                    )
                ).all()
                
                graph_context['entity_relationships'][entity_id] = [
                    {
                        'relationship_id': str(rel.id),
                        'source_entity_id': str(rel.source_entity_id),
                        'target_entity_id': str(rel.target_entity_id),
                        'relationship_type': rel.relationship_type,
                        'confidence': rel.confidence
                    }
                    for rel in relationships
                ]
            
            # Find paths between relevant entities
            if len(entity_ids) > 1:
                paths = self._find_entity_paths(entity_ids[:5])  # Limit to avoid explosion
                graph_context['relationship_paths'] = paths
            
            return graph_context
            
        except Exception as e:
            logger.error(f"Error getting graph context: {e}")
            return {}
    
    def _find_entity_paths(self, entity_ids: List[str], max_path_length: int = 3) -> List[Dict[str, Any]]:
        """
        Find paths between entities in the graph
        """
        try:
            paths = []
            
            for i, source_id in enumerate(entity_ids):
                for target_id in entity_ids[i+1:]:
                    if source_id in self.knowledge_graph.graph and target_id in self.knowledge_graph.graph:
                        try:
                            # Find shortest path
                            path = nx.shortest_path(
                                self.knowledge_graph.graph, 
                                source_id, 
                                target_id
                            )
                            
                            if len(path) <= max_path_length + 1:  # +1 because path includes endpoints
                                path_info = {
                                    'source_entity_id': source_id,
                                    'target_entity_id': target_id,
                                    'path_length': len(path) - 1,
                                    'path_entities': path,
                                    'relationships': []
                                }
                                
                                # Get relationship information for each edge in path
                                for j in range(len(path) - 1):
                                    edge_data = self.knowledge_graph.graph.get_edge_data(path[j], path[j+1])
                                    if edge_data:
                                        path_info['relationships'].append({
                                            'source': path[j],
                                            'target': path[j+1],
                                            'type': edge_data.get('relationship_type'),
                                            'confidence': edge_data.get('confidence')
                                        })
                                
                                paths.append(path_info)
                        except:
                            # No path found
                            continue
            
            return paths
            
        except Exception as e:
            logger.error(f"Error finding entity paths: {e}")
            return []
    
    def _find_relevant_summaries(self, query_embedding: List[float], 
                               limit: int = 5, db: Session = None) -> List[Dict[str, Any]]:
        """
        Find relevant graph summaries using embedding similarity
        """
        try:
            if not query_embedding:
                return []
            
            summary_query = text("""
                SELECT 
                    id,
                    level,
                    community_id,
                    title,
                    summary,
                    entities,
                    1 - (embedding <=> :query_embedding) as similarity
                FROM graph_summaries 
                WHERE embedding IS NOT NULL
                    AND 1 - (embedding <=> :query_embedding) > 0.6
                ORDER BY embedding <=> :query_embedding
                LIMIT :limit
            """)
            
            result = db.execute(summary_query, {
                'query_embedding': str(query_embedding),
                'limit': limit
            })
            
            summaries = []
            for row in result:
                summaries.append({
                    'summary_id': str(row.id),
                    'level': row.level,
                    'community_id': row.community_id,
                    'title': row.title,
                    'summary': row.summary,
                    'entities': row.entities,
                    'similarity': float(row.similarity)
                })
            
            return summaries
            
        except Exception as e:
            logger.error(f"Error finding relevant summaries: {e}")
            return []

    def _combine_context(self, chunks: List[Dict[str, Any]],
                        entities: List[Dict[str, Any]],
                        graph_context: Dict[str, Any],
                        summaries: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Combine different types of context into a unified response
        """
        try:
            combined = {
                'primary_chunks': chunks[:5],  # Top 5 most relevant chunks
                'key_entities': entities[:10],  # Top 10 most relevant entities
                'entity_connections': [],
                'contextual_summaries': summaries[:3],  # Top 3 summaries
                'total_relevance_score': 0.0
            }

            # Calculate total relevance score
            chunk_scores = [c.get('similarity', 0) for c in chunks]
            entity_scores = [e.get('relevance_score', 0) for e in entities]
            summary_scores = [s.get('similarity', 0) for s in summaries]

            if chunk_scores:
                combined['total_relevance_score'] += max(chunk_scores) * 0.4
            if entity_scores:
                combined['total_relevance_score'] += max(entity_scores) * 0.3
            if summary_scores:
                combined['total_relevance_score'] += max(summary_scores) * 0.3

            # Extract key entity connections
            if graph_context.get('relationship_paths'):
                combined['entity_connections'] = graph_context['relationship_paths'][:5]

            return combined

        except Exception as e:
            logger.error(f"Error combining context: {e}")
            return {}

    def generate_response_context(self, query_result: Dict[str, Any]) -> str:
        """
        Generate a formatted context string for LLM response generation
        """
        try:
            if not query_result.get('success'):
                return "No relevant context found."

            context_parts = []

            # Add chunk content
            if query_result.get('chunks'):
                context_parts.append("## Relevant Document Content:")
                for i, chunk in enumerate(query_result['chunks'][:3]):
                    context_parts.append(f"**Excerpt {i+1}** (similarity: {chunk.get('similarity', 0):.2f}):")
                    context_parts.append(chunk['content'][:500] + "..." if len(chunk['content']) > 500 else chunk['content'])
                    context_parts.append("")

            # Add entity information
            if query_result.get('entities'):
                context_parts.append("## Key Entities:")
                for entity in query_result['entities'][:5]:
                    context_parts.append(f"- **{entity['name']}** ({entity['type']})")
                context_parts.append("")

            # Add relationship information
            if query_result.get('graph_context', {}).get('relationship_paths'):
                context_parts.append("## Entity Relationships:")
                for path in query_result['graph_context']['relationship_paths'][:3]:
                    if path.get('relationships'):
                        rel_desc = " → ".join([
                            f"{rel['type']}" for rel in path['relationships']
                        ])
                        context_parts.append(f"- {rel_desc}")
                context_parts.append("")

            # Add summaries
            if query_result.get('summaries'):
                context_parts.append("## Contextual Summaries:")
                for summary in query_result['summaries'][:2]:
                    context_parts.append(f"**{summary['title']}**")
                    context_parts.append(summary['summary'])
                    context_parts.append("")

            return "\n".join(context_parts)

        except Exception as e:
            logger.error(f"Error generating response context: {e}")
            return "Error generating context."
