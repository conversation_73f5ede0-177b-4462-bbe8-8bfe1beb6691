"""
SQLAlchemy models for GraphRAG implementation with PostgreSQL and pgvector
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, ForeignKey, Boolean, JSON, Index
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
from pgvector.sqlalchemy import Vector
import uuid
from datetime import datetime, timezone
from app.config.database import Base

def utc_now():
    """Helper function to get current UTC time"""
    return datetime.now(timezone.utc)

class Document(Base):
    """
    Stores original documents and their metadata
    """
    __tablename__ = "documents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(500), nullable=False)
    content = Column(Text, nullable=False)
    source = Column(String(500))  # File path, URL, etc.
    document_type = Column(String(100))  # pdf, txt, html, etc.
    metadata = Column(JSON)  # Additional metadata
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)
    
    # Relationships
    chunks = relationship("DocumentChunk", back_populates="document", cascade="all, delete-orphan")
    
    # Indexes
    __table_args__ = (
        Index('idx_documents_source', 'source'),
        Index('idx_documents_type', 'document_type'),
        Index('idx_documents_created', 'created_at'),
    )

class DocumentChunk(Base):
    """
    Stores document chunks for processing and embedding
    """
    __tablename__ = "document_chunks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    chunk_index = Column(Integer, nullable=False)  # Order within document
    content = Column(Text, nullable=False)
    start_char = Column(Integer)  # Start position in original document
    end_char = Column(Integer)    # End position in original document
    token_count = Column(Integer)
    embedding = Column(Vector(384))  # Adjust dimension based on model
    created_at = Column(DateTime, default=utc_now)
    
    # Relationships
    document = relationship("Document", back_populates="chunks")
    entities = relationship("EntityMention", back_populates="chunk")
    
    # Indexes
    __table_args__ = (
        Index('idx_chunks_document', 'document_id'),
        Index('idx_chunks_embedding', 'embedding', postgresql_using='ivfflat'),
    )

class Entity(Base):
    """
    Stores unique entities extracted from documents
    """
    __tablename__ = "entities"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(500), nullable=False, unique=True)
    entity_type = Column(String(100))  # PERSON, ORG, LOCATION, etc.
    description = Column(Text)
    properties = Column(JSON)  # Additional entity properties
    embedding = Column(Vector(384))  # Entity embedding
    frequency = Column(Integer, default=1)  # How often entity appears
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)
    
    # Relationships
    mentions = relationship("EntityMention", back_populates="entity")
    source_relationships = relationship("Relationship", foreign_keys="Relationship.source_entity_id", back_populates="source_entity")
    target_relationships = relationship("Relationship", foreign_keys="Relationship.target_entity_id", back_populates="target_entity")
    
    # Indexes
    __table_args__ = (
        Index('idx_entities_name', 'name'),
        Index('idx_entities_type', 'entity_type'),
        Index('idx_entities_embedding', 'embedding', postgresql_using='ivfflat'),
        Index('idx_entities_frequency', 'frequency'),
    )

class EntityMention(Base):
    """
    Stores mentions of entities within document chunks
    """
    __tablename__ = "entity_mentions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    entity_id = Column(UUID(as_uuid=True), ForeignKey("entities.id"), nullable=False)
    chunk_id = Column(UUID(as_uuid=True), ForeignKey("document_chunks.id"), nullable=False)
    start_char = Column(Integer, nullable=False)  # Start position in chunk
    end_char = Column(Integer, nullable=False)    # End position in chunk
    confidence = Column(Float, default=1.0)      # Extraction confidence
    context = Column(Text)                       # Surrounding context
    created_at = Column(DateTime, default=utc_now)

    # Relationships
    entity = relationship("Entity", back_populates="mentions")
    chunk = relationship("DocumentChunk", back_populates="entities")

    # Indexes
    __table_args__ = (
        Index('idx_mentions_entity', 'entity_id'),
        Index('idx_mentions_chunk', 'chunk_id'),
        Index('idx_mentions_confidence', 'confidence'),
    )

class Relationship(Base):
    """
    Stores relationships between entities
    """
    __tablename__ = "relationships"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    source_entity_id = Column(UUID(as_uuid=True), ForeignKey("entities.id"), nullable=False)
    target_entity_id = Column(UUID(as_uuid=True), ForeignKey("entities.id"), nullable=False)
    relationship_type = Column(String(200), nullable=False)  # "works_for", "located_in", etc.
    description = Column(Text)
    properties = Column(JSON)  # Additional relationship properties
    confidence = Column(Float, default=1.0)
    frequency = Column(Integer, default=1)  # How often this relationship appears
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # Relationships
    source_entity = relationship("Entity", foreign_keys=[source_entity_id], back_populates="source_relationships")
    target_entity = relationship("Entity", foreign_keys=[target_entity_id], back_populates="target_relationships")

    # Indexes
    __table_args__ = (
        Index('idx_relationships_source', 'source_entity_id'),
        Index('idx_relationships_target', 'target_entity_id'),
        Index('idx_relationships_type', 'relationship_type'),
        Index('idx_relationships_confidence', 'confidence'),
        Index('idx_relationships_frequency', 'frequency'),
    )

class GraphSummary(Base):
    """
    Stores hierarchical summaries of graph communities
    """
    __tablename__ = "graph_summaries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    level = Column(Integer, nullable=False)  # Hierarchy level (0 = leaf, higher = more abstract)
    community_id = Column(String(100), nullable=False)  # Community identifier
    title = Column(String(500))
    summary = Column(Text, nullable=False)
    entities = Column(JSON)  # List of entity IDs in this community
    relationships = Column(JSON)  # List of relationship IDs in this community
    embedding = Column(Vector(384))  # Summary embedding
    created_at = Column(DateTime, default=utc_now)
    updated_at = Column(DateTime, default=utc_now, onupdate=utc_now)

    # Indexes
    __table_args__ = (
        Index('idx_summaries_level', 'level'),
        Index('idx_summaries_community', 'community_id'),
        Index('idx_summaries_embedding', 'embedding', postgresql_using='ivfflat'),
    )
