"""
Document processing pipeline for GraphRAG
Handles document ingestion, text extraction, and chunking
"""
import os
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import uuid
from datetime import datetime, timezone

# Text processing
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
import textstat

# Database
from sqlalchemy.orm import Session
from app.config.database import get_db
from app.models.graphrag_models import Document, DocumentChunk

logger = logging.getLogger(__name__)

class DocumentProcessor:
    """
    Handles document processing for GraphRAG pipeline
    """
    
    def __init__(self, max_chunk_size: int = 1000, chunk_overlap: int = 200):
        self.max_chunk_size = max_chunk_size
        self.chunk_overlap = chunk_overlap
        self._ensure_nltk_data()
    
    def _ensure_nltk_data(self):
        """Ensure required NLTK data is downloaded"""
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            logger.info("Downloading NLTK punkt tokenizer...")
            nltk.download('punkt')
    
    def extract_text_from_file(self, file_path: str) -> Optional[str]:
        """
        Extract text content from various file formats
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return None
        
        try:
            # Handle different file types
            if file_path.suffix.lower() == '.txt':
                return self._extract_from_txt(file_path)
            elif file_path.suffix.lower() == '.md':
                return self._extract_from_markdown(file_path)
            elif file_path.suffix.lower() == '.pdf':
                return self._extract_from_pdf(file_path)
            elif file_path.suffix.lower() in ['.html', '.htm']:
                return self._extract_from_html(file_path)
            else:
                logger.warning(f"Unsupported file type: {file_path.suffix}")
                return None
                
        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            return None
    
    def _extract_from_txt(self, file_path: Path) -> str:
        """Extract text from plain text file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def _extract_from_markdown(self, file_path: Path) -> str:
        """Extract text from markdown file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        # For now, return raw markdown. Could add markdown parsing later
        return content
    
    def _extract_from_pdf(self, file_path: Path) -> str:
        """Extract text from PDF file"""
        try:
            import PyPDF2
            with open(file_path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except ImportError:
            logger.error("PyPDF2 not installed. Cannot process PDF files.")
            return None
        except Exception as e:
            logger.error(f"Error processing PDF {file_path}: {e}")
            return None
    
    def _extract_from_html(self, file_path: Path) -> str:
        """Extract text from HTML file"""
        try:
            from bs4 import BeautifulSoup
            with open(file_path, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')
                return soup.get_text()
        except ImportError:
            logger.error("BeautifulSoup not installed. Cannot process HTML files.")
            return None
        except Exception as e:
            logger.error(f"Error processing HTML {file_path}: {e}")
            return None
    
    def chunk_text(self, text: str) -> List[Dict[str, Any]]:
        """
        Split text into chunks for processing
        """
        if not text:
            return []
        
        # Tokenize into sentences
        sentences = sent_tokenize(text)
        chunks = []
        current_chunk = ""
        current_start = 0
        
        for sentence in sentences:
            # Check if adding this sentence would exceed chunk size
            potential_chunk = current_chunk + " " + sentence if current_chunk else sentence
            
            if len(potential_chunk) <= self.max_chunk_size:
                current_chunk = potential_chunk
            else:
                # Save current chunk if it has content
                if current_chunk:
                    chunk_end = current_start + len(current_chunk)
                    chunks.append({
                        'content': current_chunk.strip(),
                        'start_char': current_start,
                        'end_char': chunk_end,
                        'token_count': len(word_tokenize(current_chunk))
                    })
                    
                    # Start new chunk with overlap
                    overlap_text = self._get_overlap_text(current_chunk)
                    current_start = chunk_end - len(overlap_text)
                    current_chunk = overlap_text + " " + sentence
                else:
                    # Single sentence is too long, split it
                    current_chunk = sentence
        
        # Add final chunk
        if current_chunk:
            chunk_end = current_start + len(current_chunk)
            chunks.append({
                'content': current_chunk.strip(),
                'start_char': current_start,
                'end_char': chunk_end,
                'token_count': len(word_tokenize(current_chunk))
            })
        
        return chunks
    
    def _get_overlap_text(self, text: str) -> str:
        """Get overlap text from the end of a chunk"""
        if len(text) <= self.chunk_overlap:
            return text
        return text[-self.chunk_overlap:]

    def process_document(self, file_path: str, title: Optional[str] = None,
                        metadata: Optional[Dict[str, Any]] = None,
                        db: Session = None) -> Optional[str]:
        """
        Process a document and store it in the database
        Returns the document ID if successful
        """
        if db is None:
            db = next(get_db())

        try:
            # Extract text from file
            content = self.extract_text_from_file(file_path)
            if not content:
                logger.error(f"Failed to extract content from {file_path}")
                return None

            # Create document record
            file_path_obj = Path(file_path)
            document = Document(
                title=title or file_path_obj.name,
                content=content,
                source=str(file_path),
                document_type=file_path_obj.suffix.lower().lstrip('.'),
                metadata=metadata or {}
            )

            db.add(document)
            db.flush()  # Get the document ID

            # Chunk the document
            chunks = self.chunk_text(content)

            # Store chunks
            for i, chunk_data in enumerate(chunks):
                chunk = DocumentChunk(
                    document_id=document.id,
                    chunk_index=i,
                    content=chunk_data['content'],
                    start_char=chunk_data['start_char'],
                    end_char=chunk_data['end_char'],
                    token_count=chunk_data['token_count']
                )
                db.add(chunk)

            db.commit()
            logger.info(f"Successfully processed document {file_path} with {len(chunks)} chunks")
            return str(document.id)

        except Exception as e:
            db.rollback()
            logger.error(f"Error processing document {file_path}: {e}")
            return None

    def process_text_content(self, content: str, title: str,
                           source: str = "text_input",
                           metadata: Optional[Dict[str, Any]] = None,
                           db: Session = None) -> Optional[str]:
        """
        Process text content directly and store it in the database
        Returns the document ID if successful
        """
        if db is None:
            db = next(get_db())

        try:
            # Create document record
            document = Document(
                title=title,
                content=content,
                source=source,
                document_type="text",
                metadata=metadata or {}
            )

            db.add(document)
            db.flush()  # Get the document ID

            # Chunk the document
            chunks = self.chunk_text(content)

            # Store chunks
            for i, chunk_data in enumerate(chunks):
                chunk = DocumentChunk(
                    document_id=document.id,
                    chunk_index=i,
                    content=chunk_data['content'],
                    start_char=chunk_data['start_char'],
                    end_char=chunk_data['end_char'],
                    token_count=chunk_data['token_count']
                )
                db.add(chunk)

            db.commit()
            logger.info(f"Successfully processed text content '{title}' with {len(chunks)} chunks")
            return str(document.id)

        except Exception as e:
            db.rollback()
            logger.error(f"Error processing text content '{title}': {e}")
            return None
