"""
Embedding service for GraphRAG
Handles vector embedding generation for documents, chunks, entities, and summaries
"""
import os
import logging
from typing import List, Dict, Any, Optional, Union
import numpy as np
from datetime import datetime, timezone

# Embedding models
from sentence_transformers import SentenceTransformer
import openai

# Database
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.config.database import get_db
from app.models.graphrag_models import DocumentChunk, Entity, GraphSummary

logger = logging.getLogger(__name__)

class EmbeddingService:
    """
    Service for generating and managing vector embeddings
    """
    
    def __init__(self, model_name: str = "sentence-transformers/all-MiniLM-L6-v2", 
                 use_openai: bool = False):
        self.model_name = model_name
        self.use_openai = use_openai
        self.model = None
        self.embedding_dimension = 384  # Default for all-MiniLM-L6-v2
        
        if use_openai:
            self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
            self.embedding_dimension = 1536  # OpenAI text-embedding-ada-002
        else:
            self._load_sentence_transformer()
    
    def _load_sentence_transformer(self):
        """Load sentence transformer model"""
        try:
            self.model = SentenceTransformer(self.model_name)
            self.embedding_dimension = self.model.get_sentence_embedding_dimension()
            logger.info(f"Loaded SentenceTransformer model: {self.model_name} (dim: {self.embedding_dimension})")
        except Exception as e:
            logger.error(f"Error loading SentenceTransformer model: {e}")
            raise
    
    def generate_embedding(self, text: str) -> Optional[List[float]]:
        """
        Generate embedding for a single text
        """
        try:
            if not text or not text.strip():
                return None
            
            if self.use_openai:
                return self._generate_openai_embedding(text)
            else:
                return self._generate_sentence_transformer_embedding(text)
                
        except Exception as e:
            logger.error(f"Error generating embedding: {e}")
            return None
    
    def _generate_openai_embedding(self, text: str) -> List[float]:
        """Generate embedding using OpenAI API"""
        response = self.openai_client.embeddings.create(
            model="text-embedding-ada-002",
            input=text
        )
        return response.data[0].embedding
    
    def _generate_sentence_transformer_embedding(self, text: str) -> List[float]:
        """Generate embedding using SentenceTransformer"""
        embedding = self.model.encode(text, convert_to_tensor=False)
        return embedding.tolist()
    
    def generate_batch_embeddings(self, texts: List[str]) -> List[Optional[List[float]]]:
        """
        Generate embeddings for multiple texts
        """
        try:
            if self.use_openai:
                # OpenAI API supports batch processing
                embeddings = []
                for text in texts:
                    if text and text.strip():
                        embedding = self._generate_openai_embedding(text)
                        embeddings.append(embedding)
                    else:
                        embeddings.append(None)
                return embeddings
            else:
                # SentenceTransformer batch processing
                valid_texts = []
                text_indices = []
                
                for i, text in enumerate(texts):
                    if text and text.strip():
                        valid_texts.append(text)
                        text_indices.append(i)
                
                if not valid_texts:
                    return [None] * len(texts)
                
                batch_embeddings = self.model.encode(valid_texts, convert_to_tensor=False)
                
                # Map back to original order
                result = [None] * len(texts)
                for i, embedding in enumerate(batch_embeddings):
                    original_index = text_indices[i]
                    result[original_index] = embedding.tolist()
                
                return result
                
        except Exception as e:
            logger.error(f"Error generating batch embeddings: {e}")
            return [None] * len(texts)
    
    def embed_document_chunks(self, document_id: str, db: Session = None) -> Dict[str, Any]:
        """
        Generate embeddings for all chunks of a document
        """
        if db is None:
            db = next(get_db())
        
        try:
            # Get all chunks for the document
            chunks = db.query(DocumentChunk).filter(
                DocumentChunk.document_id == document_id
            ).order_by(DocumentChunk.chunk_index).all()
            
            if not chunks:
                return {'success': False, 'error': 'No chunks found for document'}
            
            # Extract texts
            texts = [chunk.content for chunk in chunks]
            
            # Generate embeddings
            embeddings = self.generate_batch_embeddings(texts)
            
            # Update chunks with embeddings
            updated_count = 0
            for chunk, embedding in zip(chunks, embeddings):
                if embedding:
                    chunk.embedding = embedding
                    updated_count += 1
            
            db.commit()
            
            result = {
                'success': True,
                'document_id': document_id,
                'total_chunks': len(chunks),
                'updated_chunks': updated_count
            }
            
            logger.info(f"Generated embeddings for document chunks: {result}")
            return result
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error embedding document chunks: {e}")
            return {'success': False, 'error': str(e)}
    
    def embed_entities(self, batch_size: int = 100, db: Session = None) -> Dict[str, Any]:
        """
        Generate embeddings for entities
        """
        if db is None:
            db = next(get_db())
        
        try:
            # Get entities without embeddings
            entities = db.query(Entity).filter(Entity.embedding.is_(None)).all()
            
            if not entities:
                return {'success': True, 'message': 'All entities already have embeddings'}
            
            total_updated = 0
            
            # Process in batches
            for i in range(0, len(entities), batch_size):
                batch = entities[i:i + batch_size]
                
                # Create text representations for entities
                texts = []
                for entity in batch:
                    # Combine name, type, and description for embedding
                    text_parts = [entity.name]
                    if entity.entity_type:
                        text_parts.append(f"Type: {entity.entity_type}")
                    if entity.description:
                        text_parts.append(entity.description)
                    
                    entity_text = ". ".join(text_parts)
                    texts.append(entity_text)
                
                # Generate embeddings
                embeddings = self.generate_batch_embeddings(texts)
                
                # Update entities
                for entity, embedding in zip(batch, embeddings):
                    if embedding:
                        entity.embedding = embedding
                        total_updated += 1
                
                db.commit()
                logger.info(f"Processed entity batch {i//batch_size + 1}/{(len(entities) + batch_size - 1)//batch_size}")
            
            result = {
                'success': True,
                'total_entities': len(entities),
                'updated_entities': total_updated
            }
            
            logger.info(f"Generated embeddings for entities: {result}")
            return result
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error embedding entities: {e}")
            return {'success': False, 'error': str(e)}
    
    def embed_graph_summaries(self, db: Session = None) -> Dict[str, Any]:
        """
        Generate embeddings for graph summaries
        """
        if db is None:
            db = next(get_db())
        
        try:
            # Get summaries without embeddings
            summaries = db.query(GraphSummary).filter(GraphSummary.embedding.is_(None)).all()
            
            if not summaries:
                return {'success': True, 'message': 'All summaries already have embeddings'}
            
            # Extract summary texts
            texts = []
            for summary in summaries:
                # Combine title and summary for embedding
                text_parts = []
                if summary.title:
                    text_parts.append(summary.title)
                if summary.summary:
                    text_parts.append(summary.summary)
                
                summary_text = ". ".join(text_parts)
                texts.append(summary_text)
            
            # Generate embeddings
            embeddings = self.generate_batch_embeddings(texts)
            
            # Update summaries
            updated_count = 0
            for summary, embedding in zip(summaries, embeddings):
                if embedding:
                    summary.embedding = embedding
                    updated_count += 1
            
            db.commit()
            
            result = {
                'success': True,
                'total_summaries': len(summaries),
                'updated_summaries': updated_count
            }
            
            logger.info(f"Generated embeddings for graph summaries: {result}")
            return result
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error embedding graph summaries: {e}")
            return {'success': False, 'error': str(e)}
    
    def find_similar_chunks(self, query_embedding: List[float], 
                          limit: int = 10, threshold: float = 0.7,
                          db: Session = None) -> List[Dict[str, Any]]:
        """
        Find similar document chunks using vector similarity
        """
        if db is None:
            db = next(get_db())
        
        try:
            # Use pgvector cosine similarity
            query = text("""
                SELECT 
                    id,
                    document_id,
                    content,
                    chunk_index,
                    1 - (embedding <=> :query_embedding) as similarity
                FROM document_chunks 
                WHERE embedding IS NOT NULL
                    AND 1 - (embedding <=> :query_embedding) > :threshold
                ORDER BY embedding <=> :query_embedding
                LIMIT :limit
            """)
            
            result = db.execute(query, {
                'query_embedding': str(query_embedding),
                'threshold': threshold,
                'limit': limit
            })
            
            similar_chunks = []
            for row in result:
                similar_chunks.append({
                    'chunk_id': str(row.id),
                    'document_id': str(row.document_id),
                    'content': row.content,
                    'chunk_index': row.chunk_index,
                    'similarity': float(row.similarity)
                })
            
            return similar_chunks
            
        except Exception as e:
            logger.error(f"Error finding similar chunks: {e}")
            return []
