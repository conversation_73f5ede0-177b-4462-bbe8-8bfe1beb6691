"""
GraphRAG Agent that extends BaseAgent with graph-enhanced retrieval capabilities
"""
import logging
from typing import List, Dict, Any, Optional
import json

# Base agent
from app.agents.BaseAgent import BaseAgent

# GraphRAG services
from app.services.graphrag_query_engine import GraphRAGQueryEngine
from app.services.embedding_service import EmbeddingService
from app.services.document_processor import DocumentProcessor
from app.services.entity_extractor import EntityExtractor
from app.services.knowledge_graph import KnowledgeGraphBuilder

# Database
from app.config.database import get_db

logger = logging.getLogger(__name__)

class GraphRAGAgent(BaseAgent):
    """
    Agent with GraphRAG capabilities for enhanced knowledge retrieval
    """
    
    def __init__(self, model_name: str = "gpt-4", access_specifier: str = "General"):
        super().__init__(model_name, access_specifier)
        
        # Initialize GraphRAG components
        self.embedding_service = EmbeddingService()
        self.query_engine = GraphRAGQueryEngine(self.embedding_service)
        self.document_processor = DocumentProcessor()
        self.entity_extractor = EntityExtractor()
        self.knowledge_graph = KnowledgeGraphBuilder()
        
        # Update system prompt
        self.system_prompt = self._build_graphrag_system_prompt()
    
    def _build_graphrag_system_prompt(self) -> str:
        """
        Build system prompt that includes GraphRAG capabilities
        """
        base_prompt = super()._build_system_prompt()
        
        graphrag_prompt = f"""
{base_prompt}

You are enhanced with GraphRAG (Graph Retrieval-Augmented Generation) capabilities that allow you to:

1. **Knowledge Graph Access**: Query a knowledge graph containing entities, relationships, and hierarchical summaries
2. **Vector Similarity Search**: Find relevant document chunks using semantic similarity
3. **Graph Traversal**: Explore entity relationships and connections for deeper context
4. **Document Processing**: Ingest and process new documents to expand the knowledge base

When answering questions, you can:
- Retrieve relevant information from the knowledge graph
- Find semantically similar content using vector search
- Explore entity relationships for comprehensive understanding
- Access hierarchical summaries for high-level context

Use these capabilities to provide more accurate, contextual, and comprehensive responses.
"""
        return graphrag_prompt
    
    def query_knowledge_graph(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Query the knowledge graph for relevant information
        """
        try:
            db = next(get_db())
            result = self.query_engine.query(
                query_text=query,
                max_chunks=kwargs.get('max_chunks', 10),
                max_entities=kwargs.get('max_entities', 20),
                similarity_threshold=kwargs.get('similarity_threshold', 0.7),
                include_graph_context=kwargs.get('include_graph_context', True),
                db=db
            )
            
            logger.info(f"GraphRAG query completed for: {query}")
            return result
            
        except Exception as e:
            logger.error(f"Error querying knowledge graph: {e}")
            return {'success': False, 'error': str(e)}
    
    def process_document(self, file_path: str, title: Optional[str] = None, 
                        metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a document and add it to the knowledge graph
        """
        try:
            db = next(get_db())
            
            # Step 1: Process document and create chunks
            document_id = self.document_processor.process_document(
                file_path=file_path,
                title=title,
                metadata=metadata,
                db=db
            )
            
            if not document_id:
                return {'success': False, 'error': 'Failed to process document'}
            
            # Step 2: Extract entities and relationships
            extraction_result = self.entity_extractor.process_document_chunks(document_id, db)
            
            if not extraction_result['success']:
                return {'success': False, 'error': 'Failed to extract entities'}
            
            # Step 3: Generate embeddings
            embedding_result = self.embedding_service.embed_document_chunks(document_id, db)
            
            if not embedding_result['success']:
                logger.warning(f"Failed to generate embeddings for document {document_id}")
            
            # Step 4: Update knowledge graph and create summaries
            graph_result = self.knowledge_graph.build_graph_from_database(db)
            summary_result = self.knowledge_graph.create_hierarchical_summaries(db=db)
            
            # Step 5: Generate embeddings for new entities and summaries
            self.embedding_service.embed_entities(db=db)
            self.embedding_service.embed_graph_summaries(db=db)
            
            result = {
                'success': True,
                'document_id': document_id,
                'entities_extracted': extraction_result.get('total_entities', 0),
                'relationships_extracted': extraction_result.get('total_relationships', 0),
                'chunks_embedded': embedding_result.get('updated_chunks', 0),
                'summaries_created': summary_result.get('summaries_created', 0)
            }
            
            logger.info(f"Successfully processed document: {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing document {file_path}: {e}")
            return {'success': False, 'error': str(e)}
    
    def process_text_content(self, content: str, title: str, 
                           source: str = "text_input",
                           metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process text content and add it to the knowledge graph
        """
        try:
            db = next(get_db())
            
            # Step 1: Process text content
            document_id = self.document_processor.process_text_content(
                content=content,
                title=title,
                source=source,
                metadata=metadata,
                db=db
            )
            
            if not document_id:
                return {'success': False, 'error': 'Failed to process text content'}
            
            # Step 2: Extract entities and relationships
            extraction_result = self.entity_extractor.process_document_chunks(document_id, db)
            
            # Step 3: Generate embeddings
            embedding_result = self.embedding_service.embed_document_chunks(document_id, db)
            
            # Step 4: Update knowledge graph
            self.knowledge_graph.build_graph_from_database(db)
            self.embedding_service.embed_entities(db=db)
            
            result = {
                'success': True,
                'document_id': document_id,
                'entities_extracted': extraction_result.get('total_entities', 0),
                'relationships_extracted': extraction_result.get('total_relationships', 0),
                'chunks_embedded': embedding_result.get('updated_chunks', 0)
            }
            
            logger.info(f"Successfully processed text content '{title}': {result}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing text content '{title}': {e}")
            return {'success': False, 'error': str(e)}
    
    def reply(self, message: str, context: Dict[str, Any] = None) -> str:
        """
        Enhanced reply method that uses GraphRAG for context retrieval
        """
        try:
            # Query knowledge graph for relevant context
            kg_result = self.query_knowledge_graph(message)
            
            if kg_result.get('success'):
                # Generate formatted context for LLM
                graph_context = self.query_engine.generate_response_context(kg_result)
                
                # Combine with any existing context
                full_context = context or {}
                full_context['graphrag_context'] = graph_context
                full_context['kg_stats'] = {
                    'chunks_found': len(kg_result.get('chunks', [])),
                    'entities_found': len(kg_result.get('entities', [])),
                    'summaries_found': len(kg_result.get('summaries', []))
                }
                
                # Generate response using base agent logic with enhanced context
                base_response = super().reply(message, full_context)
                
                # Add GraphRAG metadata to response
                enhanced_response = f"{base_response}\n\n[GraphRAG Context: {full_context['kg_stats']}]"
                
                return enhanced_response
            else:
                # Fallback to base agent if GraphRAG fails
                logger.warning("GraphRAG query failed, using base agent response")
                return super().reply(message, context)
                
        except Exception as e:
            logger.error(f"Error in GraphRAG reply: {e}")
            return super().reply(message, context)
    
    def get_entity_information(self, entity_name: str) -> Dict[str, Any]:
        """
        Get detailed information about a specific entity
        """
        try:
            db = next(get_db())
            
            # Build knowledge graph
            self.knowledge_graph.build_graph_from_database(db)
            
            # Find entity by name
            from app.models.graphrag_models import Entity
            entity = db.query(Entity).filter(Entity.name.ilike(f'%{entity_name}%')).first()
            
            if not entity:
                return {'success': False, 'error': 'Entity not found'}
            
            # Get entity neighbors and relationships
            neighbors = self.knowledge_graph.get_entity_neighbors(str(entity.id), max_hops=2)
            
            result = {
                'success': True,
                'entity': {
                    'id': str(entity.id),
                    'name': entity.name,
                    'type': entity.entity_type,
                    'frequency': entity.frequency,
                    'description': entity.description
                },
                'neighbors': neighbors,
                'neighbor_count': len(neighbors)
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting entity information: {e}")
            return {'success': False, 'error': str(e)}
