"""
FastAPI router for GraphRAG operations
"""
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import tempfile
import os

from fastapi import APIRouter, HTTPException, UploadFile, File, Depends, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# Database
from sqlalchemy.orm import Session
from app.config.database import get_db

# GraphRAG components
from app.agents.GraphRAGAgent import GraphRAGAgent
from app.services.graphrag_query_engine import GraphRAGQueryEngine
from app.services.embedding_service import EmbeddingService
from app.services.document_processor import DocumentProcessor
from app.services.entity_extractor import EntityExtractor
from app.services.knowledge_graph import KnowledgeGraphBuilder

logger = logging.getLogger(__name__)

# Initialize router
router = APIRouter(prefix="/graphrag", tags=["GraphRAG"])

# Initialize GraphRAG components
graphrag_agent = GraphRAGAgent()
query_engine = GraphRAGQueryEngine()
embedding_service = EmbeddingService()
document_processor = DocumentProcessor()
entity_extractor = EntityExtractor()
knowledge_graph = KnowledgeGraphBuilder()

# Pydantic models for request/response
class QueryRequest(BaseModel):
    query: str = Field(..., description="The query text")
    max_chunks: int = Field(10, description="Maximum number of chunks to retrieve")
    max_entities: int = Field(20, description="Maximum number of entities to retrieve")
    similarity_threshold: float = Field(0.7, description="Similarity threshold for retrieval")
    include_graph_context: bool = Field(True, description="Whether to include graph context")

class TextContentRequest(BaseModel):
    content: str = Field(..., description="Text content to process")
    title: str = Field(..., description="Title for the content")
    source: str = Field("api_input", description="Source identifier")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class EntityQueryRequest(BaseModel):
    entity_name: str = Field(..., description="Name of the entity to query")

# API Endpoints

@router.post("/query")
async def query_knowledge_graph(
    request: QueryRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Query the knowledge graph using GraphRAG
    """
    try:
        result = query_engine.query(
            query_text=request.query,
            max_chunks=request.max_chunks,
            max_entities=request.max_entities,
            similarity_threshold=request.similarity_threshold,
            include_graph_context=request.include_graph_context,
            db=db
        )
        
        if result.get('success'):
            return JSONResponse(
                status_code=200,
                content={
                    "success": True,
                    "query": request.query,
                    "results": result,
                    "context": query_engine.generate_response_context(result)
                }
            )
        else:
            raise HTTPException(status_code=500, detail=result.get('error', 'Query failed'))
            
    except Exception as e:
        logger.error(f"Error in GraphRAG query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/chat")
async def chat_with_graphrag(
    request: QueryRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Chat with GraphRAG agent
    """
    try:
        response = graphrag_agent.reply(request.query)
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "query": request.query,
                "response": response,
                "agent": "GraphRAGAgent"
            }
        )
        
    except Exception as e:
        logger.error(f"Error in GraphRAG chat: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/upload-document")
async def upload_document(
    file: UploadFile = File(...),
    title: Optional[str] = Query(None, description="Document title"),
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Upload and process a document
    """
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        try:
            # Process the document
            result = graphrag_agent.process_document(
                file_path=tmp_file_path,
                title=title or file.filename,
                metadata={
                    "original_filename": file.filename,
                    "content_type": file.content_type,
                    "file_size": len(content)
                }
            )
            
            if result.get('success'):
                return JSONResponse(
                    status_code=200,
                    content={
                        "success": True,
                        "message": "Document processed successfully",
                        "document_id": result['document_id'],
                        "stats": {
                            "entities_extracted": result.get('entities_extracted', 0),
                            "relationships_extracted": result.get('relationships_extracted', 0),
                            "chunks_embedded": result.get('chunks_embedded', 0),
                            "summaries_created": result.get('summaries_created', 0)
                        }
                    }
                )
            else:
                raise HTTPException(status_code=500, detail=result.get('error', 'Document processing failed'))
                
        finally:
            # Clean up temporary file
            os.unlink(tmp_file_path)
            
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/process-text")
async def process_text_content(
    request: TextContentRequest,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Process text content and add to knowledge graph
    """
    try:
        result = graphrag_agent.process_text_content(
            content=request.content,
            title=request.title,
            source=request.source,
            metadata=request.metadata
        )
        
        if result.get('success'):
            return JSONResponse(
                status_code=200,
                content={
                    "success": True,
                    "message": "Text content processed successfully",
                    "document_id": result['document_id'],
                    "stats": {
                        "entities_extracted": result.get('entities_extracted', 0),
                        "relationships_extracted": result.get('relationships_extracted', 0),
                        "chunks_embedded": result.get('chunks_embedded', 0)
                    }
                }
            )
        else:
            raise HTTPException(status_code=500, detail=result.get('error', 'Text processing failed'))
            
    except Exception as e:
        logger.error(f"Error processing text content: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/entity/{entity_name}")
async def get_entity_info(
    entity_name: str,
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Get information about a specific entity
    """
    try:
        result = graphrag_agent.get_entity_information(entity_name)
        
        if result.get('success'):
            return JSONResponse(
                status_code=200,
                content={
                    "success": True,
                    "entity": result['entity'],
                    "neighbors": result['neighbors'],
                    "neighbor_count": result['neighbor_count']
                }
            )
        else:
            raise HTTPException(status_code=404, detail=result.get('error', 'Entity not found'))
            
    except Exception as e:
        logger.error(f"Error getting entity info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_knowledge_graph_stats(
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Get knowledge graph statistics
    """
    try:
        from app.models.graphrag_models import Document, DocumentChunk, Entity, Relationship, GraphSummary
        
        # Get counts from database
        document_count = db.query(Document).count()
        chunk_count = db.query(DocumentChunk).count()
        entity_count = db.query(Entity).count()
        relationship_count = db.query(Relationship).count()
        summary_count = db.query(GraphSummary).count()
        
        # Get embedding statistics
        chunks_with_embeddings = db.query(DocumentChunk).filter(DocumentChunk.embedding.isnot(None)).count()
        entities_with_embeddings = db.query(Entity).filter(Entity.embedding.isnot(None)).count()
        summaries_with_embeddings = db.query(GraphSummary).filter(GraphSummary.embedding.isnot(None)).count()
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "stats": {
                    "documents": document_count,
                    "chunks": chunk_count,
                    "entities": entity_count,
                    "relationships": relationship_count,
                    "summaries": summary_count,
                    "embeddings": {
                        "chunks_with_embeddings": chunks_with_embeddings,
                        "entities_with_embeddings": entities_with_embeddings,
                        "summaries_with_embeddings": summaries_with_embeddings
                    }
                }
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/rebuild-graph")
async def rebuild_knowledge_graph(
    db: Session = Depends(get_db)
) -> JSONResponse:
    """
    Rebuild the knowledge graph and create new summaries
    """
    try:
        # Rebuild graph from database
        graph_result = knowledge_graph.build_graph_from_database(db)
        
        if not graph_result.get('success'):
            raise HTTPException(status_code=500, detail="Failed to build graph")
        
        # Create hierarchical summaries
        summary_result = knowledge_graph.create_hierarchical_summaries(db=db)
        
        # Generate embeddings for new summaries
        embedding_result = embedding_service.embed_graph_summaries(db=db)
        
        return JSONResponse(
            status_code=200,
            content={
                "success": True,
                "message": "Knowledge graph rebuilt successfully",
                "graph_stats": graph_result['stats'],
                "summaries_created": summary_result.get('summaries_created', 0),
                "summaries_embedded": embedding_result.get('updated_summaries', 0)
            }
        )
        
    except Exception as e:
        logger.error(f"Error rebuilding graph: {e}")
        raise HTTPException(status_code=500, detail=str(e))
