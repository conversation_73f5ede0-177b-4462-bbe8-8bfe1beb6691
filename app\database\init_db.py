"""
Database initialization script for GraphRAG
"""
import logging
from app.config.database import init_database, test_connection
from app.models.graphrag_models import *  # Import all models to register them

logger = logging.getLogger(__name__)

def initialize_graphrag_database():
    """
    Initialize the GraphRAG database with all required tables and extensions
    """
    try:
        logger.info("Starting GraphRAG database initialization...")
        
        # Test connection first
        if not test_connection():
            raise Exception("Database connection failed")
        
        # Initialize database with extensions and tables
        init_database()
        
        logger.info("GraphRAG database initialization completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize GraphRAG database: {e}")
        return False

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Initialize database
    success = initialize_graphrag_database()
    if success:
        print("Database initialization successful!")
    else:
        print("Database initialization failed!")
        exit(1)
