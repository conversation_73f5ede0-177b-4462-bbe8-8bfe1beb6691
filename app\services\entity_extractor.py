"""
Entity extraction and relationship detection for GraphRAG
Uses spaCy for NLP processing and custom rules for relationship extraction
"""
import os
import logging
from typing import List, Dict, Any, Tuple, Optional, Set
import re
from collections import defaultdict

# NLP
import spacy
from spacy.matcher import Matcher
from spacy.tokens import Doc, Span

# Database
from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.config.database import get_db
from app.models.graphrag_models import Entity, EntityMention, Relationship, DocumentChunk

logger = logging.getLogger(__name__)

class EntityExtractor:
    """
    Extracts entities and relationships from text using spaCy
    """
    
    def __init__(self, model_name: str = "en_core_web_sm"):
        self.model_name = model_name
        self.nlp = None
        self.matcher = None
        self._load_model()
        self._setup_patterns()
    
    def _load_model(self):
        """Load spaCy model"""
        try:
            self.nlp = spacy.load(self.model_name)
            logger.info(f"Loaded spaCy model: {self.model_name}")
        except OSError:
            logger.error(f"spaCy model '{self.model_name}' not found. Please install it with: python -m spacy download {self.model_name}")
            raise
    
    def _setup_patterns(self):
        """Setup custom patterns for relationship extraction"""
        self.matcher = Matcher(self.nlp.vocab)
        
        # Relationship patterns
        # Pattern for "X works for Y"
        works_for_pattern = [
            {"LOWER": {"IN": ["works", "worked", "working"]}},
            {"LOWER": {"IN": ["for", "at", "with"]}},
            {"ENT_TYPE": {"IN": ["ORG", "PERSON"]}}
        ]
        
        # Pattern for "X is located in Y"
        located_in_pattern = [
            {"LOWER": {"IN": ["located", "based", "situated"]}},
            {"LOWER": "in"},
            {"ENT_TYPE": {"IN": ["GPE", "LOC"]}}
        ]
        
        # Pattern for "X founded Y"
        founded_pattern = [
            {"ENT_TYPE": "PERSON"},
            {"LOWER": {"IN": ["founded", "established", "created"]}},
            {"ENT_TYPE": "ORG"}
        ]
        
        self.matcher.add("WORKS_FOR", [works_for_pattern])
        self.matcher.add("LOCATED_IN", [located_in_pattern])
        self.matcher.add("FOUNDED", [founded_pattern])
    
    def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract entities from text
        """
        doc = self.nlp(text)
        entities = []
        
        for ent in doc.ents:
            # Filter out very short entities and common stop words
            if len(ent.text.strip()) < 2:
                continue
                
            entity_data = {
                'text': ent.text.strip(),
                'label': ent.label_,
                'start_char': ent.start_char,
                'end_char': ent.end_char,
                'confidence': 1.0  # spaCy doesn't provide confidence scores by default
            }
            entities.append(entity_data)
        
        return entities
    
    def extract_relationships(self, text: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract relationships between entities
        """
        doc = self.nlp(text)
        relationships = []
        
        # Use pattern matching for explicit relationships
        matches = self.matcher(doc)
        for match_id, start, end in matches:
            span = doc[start:end]
            label = self.nlp.vocab.strings[match_id]
            
            # Extract entities involved in the relationship
            entities_in_span = [ent for ent in doc.ents if ent.start >= start and ent.end <= end]
            
            if len(entities_in_span) >= 2:
                relationships.append({
                    'source_entity': entities_in_span[0].text,
                    'target_entity': entities_in_span[1].text,
                    'relationship_type': label.lower(),
                    'confidence': 0.8,
                    'context': span.text
                })
        
        # Extract co-occurrence relationships (entities appearing in same sentence)
        for sent in doc.sents:
            sent_entities = [ent for ent in doc.ents if ent.start >= sent.start and ent.end <= sent.end]
            
            # Create co-occurrence relationships for entities in the same sentence
            for i, ent1 in enumerate(sent_entities):
                for ent2 in sent_entities[i+1:]:
                    if ent1.text != ent2.text:
                        relationships.append({
                            'source_entity': ent1.text,
                            'target_entity': ent2.text,
                            'relationship_type': 'co_occurs_with',
                            'confidence': 0.5,
                            'context': sent.text
                        })
        
        return relationships
    
    def process_chunk(self, chunk_id: str, db: Session = None) -> Dict[str, Any]:
        """
        Process a document chunk to extract entities and relationships
        """
        if db is None:
            db = next(get_db())
        
        try:
            # Get the chunk
            chunk = db.query(DocumentChunk).filter(DocumentChunk.id == chunk_id).first()
            if not chunk:
                logger.error(f"Chunk not found: {chunk_id}")
                return {'success': False, 'error': 'Chunk not found'}
            
            # Extract entities
            entities_data = self.extract_entities(chunk.content)
            
            # Store entities and mentions
            stored_entities = []
            for entity_data in entities_data:
                entity = self._get_or_create_entity(
                    entity_data['text'], 
                    entity_data['label'], 
                    db
                )
                
                # Create entity mention
                mention = EntityMention(
                    entity_id=entity.id,
                    chunk_id=chunk.id,
                    start_char=entity_data['start_char'],
                    end_char=entity_data['end_char'],
                    confidence=entity_data['confidence'],
                    context=chunk.content[max(0, entity_data['start_char']-50):entity_data['end_char']+50]
                )
                db.add(mention)
                stored_entities.append(entity)
            
            # Extract relationships
            relationships_data = self.extract_relationships(chunk.content, entities_data)
            
            # Store relationships
            stored_relationships = []
            for rel_data in relationships_data:
                source_entity = self._get_entity_by_name(rel_data['source_entity'], db)
                target_entity = self._get_entity_by_name(rel_data['target_entity'], db)
                
                if source_entity and target_entity:
                    relationship = self._get_or_create_relationship(
                        source_entity.id,
                        target_entity.id,
                        rel_data['relationship_type'],
                        rel_data.get('confidence', 0.5),
                        db
                    )
                    stored_relationships.append(relationship)
            
            db.commit()
            
            result = {
                'success': True,
                'entities_count': len(stored_entities),
                'relationships_count': len(stored_relationships),
                'chunk_id': chunk_id
            }
            
            logger.info(f"Processed chunk {chunk_id}: {result}")
            return result
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error processing chunk {chunk_id}: {e}")
            return {'success': False, 'error': str(e)}

    def _get_or_create_entity(self, name: str, entity_type: str, db: Session) -> Entity:
        """
        Get existing entity or create new one
        """
        # Normalize entity name
        normalized_name = name.strip().lower()

        # Check if entity already exists
        entity = db.query(Entity).filter(Entity.name.ilike(normalized_name)).first()

        if entity:
            # Update frequency
            entity.frequency += 1
            return entity
        else:
            # Create new entity
            entity = Entity(
                name=normalized_name,
                entity_type=entity_type,
                frequency=1
            )
            db.add(entity)
            db.flush()  # Get the ID
            return entity

    def _get_entity_by_name(self, name: str, db: Session) -> Optional[Entity]:
        """
        Get entity by name
        """
        normalized_name = name.strip().lower()
        return db.query(Entity).filter(Entity.name.ilike(normalized_name)).first()

    def _get_or_create_relationship(self, source_id: str, target_id: str,
                                  rel_type: str, confidence: float, db: Session) -> Relationship:
        """
        Get existing relationship or create new one
        """
        # Check if relationship already exists
        relationship = db.query(Relationship).filter(
            and_(
                Relationship.source_entity_id == source_id,
                Relationship.target_entity_id == target_id,
                Relationship.relationship_type == rel_type
            )
        ).first()

        if relationship:
            # Update frequency and confidence
            relationship.frequency += 1
            relationship.confidence = max(relationship.confidence, confidence)
            return relationship
        else:
            # Create new relationship
            relationship = Relationship(
                source_entity_id=source_id,
                target_entity_id=target_id,
                relationship_type=rel_type,
                confidence=confidence,
                frequency=1
            )
            db.add(relationship)
            db.flush()  # Get the ID
            return relationship

    def process_document_chunks(self, document_id: str, db: Session = None) -> Dict[str, Any]:
        """
        Process all chunks of a document
        """
        if db is None:
            db = next(get_db())

        try:
            # Get all chunks for the document
            chunks = db.query(DocumentChunk).filter(
                DocumentChunk.document_id == document_id
            ).order_by(DocumentChunk.chunk_index).all()

            if not chunks:
                return {'success': False, 'error': 'No chunks found for document'}

            total_entities = 0
            total_relationships = 0
            processed_chunks = 0

            for chunk in chunks:
                result = self.process_chunk(str(chunk.id), db)
                if result['success']:
                    total_entities += result['entities_count']
                    total_relationships += result['relationships_count']
                    processed_chunks += 1
                else:
                    logger.warning(f"Failed to process chunk {chunk.id}: {result.get('error')}")

            return {
                'success': True,
                'document_id': document_id,
                'processed_chunks': processed_chunks,
                'total_chunks': len(chunks),
                'total_entities': total_entities,
                'total_relationships': total_relationships
            }

        except Exception as e:
            logger.error(f"Error processing document chunks for {document_id}: {e}")
            return {'success': False, 'error': str(e)}
